/** @type {import('tailwindcss').Config} */
export default {
  content: ['./src/**/*.{html,js,svelte,ts}'],
  theme: {
    extend: {
      fontFamily: {
        'sans': ['Inter', 'sans-serif'],
        'serif': ['Playfair Display', 'serif'],
        'arabic': ['Noto Kufi Arabic', 'sans-serif'],
      },
      colors: {
        'rose-gold': {
          50: '#FAF8F5',
          100: '#F5F1EB',
          200: '#E8C4A0',
          300: '#D4A574',
          400: '#B8956A',
          500: '#A67C52',
          600: '#8B6B47',
          700: '#6B5238',
          800: '#4A392A',
          900: '#2D221C',
        },
        'warm-beige': '#F5F1EB',
        'cream': '#FAF8F5',
        'charcoal': '#2D2D2D',
        'soft-gray': '#8B8B8B',
        'accent-pink': '#F4E4E0',
        'luxury-purple': '#6B46C1',
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem',
      },
      borderRadius: {
        'xl': '1rem',
        '2xl': '1.5rem',
        '3xl': '2rem',
      },
      boxShadow: {
        'jewelry': '0 4px 20px rgba(212, 165, 116, 0.1)',
        'jewelry-hover': '0 8px 30px rgba(212, 165, 116, 0.2)',
        'luxury': '0 10px 40px rgba(0, 0, 0, 0.1)',
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'bounce-gentle': 'bounceGentle 2s infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        bounceGentle: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-5px)' },
        },
      },
    },
  },
  plugins: [],
}
