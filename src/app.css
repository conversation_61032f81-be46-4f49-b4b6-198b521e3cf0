@import 'tailwindcss';
@plugin '@tailwindcss/forms';
@plugin '@tailwindcss/typography';

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap');
/* Import Noto Kufi Arabic specifically for Kurdish Sorani */
@import url('https://fonts.googleapis.com/css2?family=Noto+Kufi+Arabic:wght@300;400;500;600;700&subset=arabic&display=swap');

/* Custom CSS Variables for Jewelry Theme */
:root {
  --rose-gold: #D4A574;
  --rose-gold-light: #E8C4A0;
  --rose-gold-dark: #B8956A;
  --warm-beige: #F5F1EB;
  --cream: #FAF8F5;
  --charcoal: #2D2D2D;
  --soft-gray: #8B8B8B;
  --accent-pink: #F4E4E0;
  --luxury-purple: #6B46C1;
}

/* Base Typography */
body {
  font-family: 'Inter', sans-serif;
  color: var(--charcoal);
  background-color: var(--cream);
}

/* Kurdish Sorani Text Styling */
[lang="ku"], .kurdish-text {
  font-family: 'Noto Kufi Arabic', 'Arial Unicode MS', sans-serif;
  direction: rtl;
  text-align: right;
  font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
  letter-spacing: 0.02em;
}

/* Enhanced Kurdish text for headings */
[lang="ku"] h1, [lang="ku"] h2, [lang="ku"] h3, [lang="ku"] h4, [lang="ku"] h5, [lang="ku"] h6,
.kurdish-text h1, .kurdish-text h2, .kurdish-text h3, .kurdish-text h4, .kurdish-text h5, .kurdish-text h6 {
  font-family: 'Noto Kufi Arabic', 'Arial Unicode MS', sans-serif;
  font-weight: 600;
  line-height: 1.4;
  letter-spacing: 0.01em;
}

/* English Text Styling */
[lang="en"], .english-text {
  font-family: 'Inter', sans-serif;
  direction: ltr;
  text-align: left;
  line-height: 1.6;
}

/* Elegant Headings */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Playfair Display', serif;
  font-weight: 600;
  color: var(--charcoal);
}

/* Jewelry-specific styling */
.jewelry-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(212, 165, 116, 0.1);
  transition: all 0.3s ease;
}

.jewelry-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(212, 165, 116, 0.2);
}

.rose-gold-gradient {
  background: linear-gradient(135deg, var(--rose-gold-light), var(--rose-gold));
}

.luxury-button {
  background: var(--rose-gold);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
}

.luxury-button:hover {
  background: var(--rose-gold-dark);
  transform: translateY(-1px);
}

.elegant-input {
  border: 2px solid #E5E7EB;
  border-radius: 8px;
  padding: 12px 16px;
  transition: all 0.3s ease;
  background: white;
}

.elegant-input:focus {
  border-color: var(--rose-gold);
  outline: none;
  box-shadow: 0 0 0 3px rgba(212, 165, 116, 0.1);
}

/* Responsive jewelry grid */
.jewelry-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 24px;
}

@media (max-width: 640px) {
  .jewelry-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 16px;
  }
}

/* Kurdish text responsive adjustments */
@media (max-width: 768px) {
  [lang="ku"], .kurdish-text {
    font-size: 0.95em;
    line-height: 1.7;
  }

  [lang="ku"] h1, .kurdish-text h1 {
    font-size: 1.8rem;
  }

  [lang="ku"] h2, .kurdish-text h2 {
    font-size: 1.5rem;
  }
}

/* Improved Kurdish text selection */
[lang="ku"]::selection, .kurdish-text::selection {
  background-color: var(--rose-gold-200);
  color: var(--charcoal);
}

/* Better Kurdish input styling */
input[lang="ku"], textarea[lang="ku"], .kurdish-input {
  font-family: 'Noto Kufi Arabic', 'Arial Unicode MS', sans-serif;
  direction: rtl;
  text-align: right;
  font-feature-settings: "kern" 1, "liga" 1;
}
