<script lang="ts">
	import { currentLanguage, t } from '$lib/stores/language.js';
	import { addToCart } from '$lib/stores/cart.js';
	import { Search, Grid, List, Star, Heart, Crown, Sparkles, Gem, Gift } from 'lucide-svelte';

	// Jewelry products data
	let products = [
		{
			id: 1,
			name: { en: 'Rose Gold Heart Necklace', ku: 'ملوانکەی دڵی ئاڵتونی گوڵاوی' },
			description: { en: 'Elegant 18k rose gold necklace with heart pendant, perfect for romantic occasions', ku: 'ملوانکەی ئەنیقی ئاڵتونی گوڵاوی ١٨ قیرات لەگەڵ پەندەنتی دڵ، تەواو بۆ بۆنە ڕۆمانسیەکان' },
			price: 189.99,
			image: '/api/placeholder/300/300',
			category: { en: 'Necklaces', ku: 'ملوانکە' },
			rating: 4.9,
			inStock: true,
			material: { en: '18k Rose Gold', ku: 'ئاڵتونی گوڵاوی ١٨ قیرات' },
			collection: { en: 'Romantic', ku: 'ڕۆمانسی' }
		},
		{
			id: 2,
			name: { en: 'Diamond Sparkle Bracelet', ku: 'بازنی بریسکەی ئەڵماس' },
			description: { en: 'Stunning tennis bracelet featuring brilliant cut diamonds in white gold setting', ku: 'بازنی تێنیسی سەرسوڕهێنەر کە ئەڵماسی بریسکەداری لە ئاڵتونی سپی تێدایە' },
			price: 459.99,
			image: '/api/placeholder/300/300',
			category: { en: 'Bracelets', ku: 'بازن' },
			rating: 4.8,
			inStock: true,
			material: { en: 'White Gold & Diamonds', ku: 'ئاڵتونی سپی و ئەڵماس' },
			collection: { en: 'Luxury', ku: 'لوکس' }
		},
		{
			id: 3,
			name: { en: 'Pearl Drop Earrings', ku: 'گوارەی دڵۆپی مروارید' },
			description: { en: 'Classic freshwater pearl earrings with sterling silver hooks', ku: 'گوارەی کلاسیکی مرواریدی ئاوی شیرین لەگەڵ قولاپی زیوی ستێرلینگ' },
			price: 124.99,
			image: '/api/placeholder/300/300',
			category: { en: 'Earrings', ku: 'گوارە' },
			rating: 4.7,
			inStock: true,
			material: { en: 'Sterling Silver & Pearls', ku: 'زیوی ستێرلینگ و مروارید' },
			collection: { en: 'Classic', ku: 'کلاسیک' }
		},
		{
			id: 4,
			name: { en: 'Royal Crown Ring', ku: 'ئەنگوستیلەی تاجی شاهانە' },
			description: { en: 'Majestic crown-shaped ring with sapphires and diamonds in platinum', ku: 'ئەنگوستیلەی شاهانەی شێوەی تاج لەگەڵ یاقووت و ئەڵماس لە پلاتین' },
			price: 899.99,
			image: '/api/placeholder/300/300',
			category: { en: 'Rings', ku: 'ئەنگوستیلە' },
			rating: 5.0,
			inStock: false,
			material: { en: 'Platinum, Sapphires & Diamonds', ku: 'پلاتین، یاقووت و ئەڵماس' },
			collection: { en: 'Royal', ku: 'شاهانە' }
		},
		{
			id: 5,
			name: { en: 'Elegant Jewelry Set', ku: 'کۆمەڵەی خشڵی ئەنیق' },
			description: { en: 'Complete jewelry set including necklace, earrings, and bracelet in matching design', ku: 'کۆمەڵەی تەواوی خشڵ کە ملوانکە، گوارە و بازنی هاوشێوە لەخۆدەگرێت' },
			price: 349.99,
			image: '/api/placeholder/300/300',
			category: { en: 'Jewelry Sets', ku: 'کۆمەڵەی خشڵ' },
			rating: 4.6,
			inStock: true,
			material: { en: '14k Gold & Crystals', ku: 'ئاڵتونی ١٤ قیرات و کریستاڵ' },
			collection: { en: 'Elegant', ku: 'ئەنیق' }
		},
		{
			id: 6,
			name: { en: 'Emerald Gemstone Ring', ku: 'ئەنگوستیلەی بەردی زومورود' },
			description: { en: 'Exquisite emerald ring with diamond accents in yellow gold band', ku: 'ئەنگوستیلەی زومورودی ئەنیق لەگەڵ ئەڵماسی ڕازاوە لە ئاڵتونی زەرد' },
			price: 679.99,
			image: '/api/placeholder/300/300',
			category: { en: 'Precious Stones', ku: 'بەردی گرانبەها' },
			rating: 4.9,
			inStock: true,
			material: { en: 'Yellow Gold & Emerald', ku: 'ئاڵتونی زەرد و زومورود' },
			collection: { en: 'Gemstone', ku: 'بەردی گرانبەها' }
		},
		{
			id: 7,
			name: { en: 'Vintage Charm Bracelet', ku: 'بازنی چارمی کۆن' },
			description: { en: 'Vintage-inspired charm bracelet with multiple decorative charms', ku: 'بازنی چارمی ئیلهامگیراو لە کۆن لەگەڵ چەندین چارمی ڕازاوە' },
			price: 199.99,
			image: '/api/placeholder/300/300',
			category: { en: 'Bracelets', ku: 'بازن' },
			rating: 4.4,
			inStock: true,
			material: { en: 'Sterling Silver', ku: 'زیوی ستێرلینگ' },
			collection: { en: 'Vintage', ku: 'کۆن' }
		},
		{
			id: 8,
			name: { en: 'Crystal Stud Earrings', ku: 'گوارەی کریستاڵی ستاد' },
			description: { en: 'Sparkling crystal stud earrings perfect for everyday elegance', ku: 'گوارەی ستادی کریستاڵی بریسکەدار تەواو بۆ ئەنیقی ڕۆژانە' },
			price: 79.99,
			image: '/api/placeholder/300/300',
			category: { en: 'Earrings', ku: 'گوارە' },
			rating: 4.5,
			inStock: true,
			material: { en: 'Sterling Silver & Crystals', ku: 'زیوی ستێرلینگ و کریستاڵ' },
			collection: { en: 'Everyday', ku: 'ڕۆژانە' }
		}
	];

	let searchTerm = '';
	let selectedCategory = '';
	let viewMode = 'grid'; // 'grid' or 'list'
	let sortBy = 'name'; // 'name', 'price', 'rating'

	// Get unique categories
	$: categories = [...new Set(products.map(p => p.category[$currentLanguage]))];

	// Filter and sort products
	$: filteredProducts = products
		.filter(product => {
			const matchesSearch = product.name[$currentLanguage].toLowerCase().includes(searchTerm.toLowerCase()) ||
								product.description[$currentLanguage].toLowerCase().includes(searchTerm.toLowerCase());
			const matchesCategory = !selectedCategory || product.category[$currentLanguage] === selectedCategory;
			return matchesSearch && matchesCategory;
		})
		.sort((a, b) => {
			switch (sortBy) {
				case 'price':
					return a.price - b.price;
				case 'rating':
					return b.rating - a.rating;
				default:
					return a.name[$currentLanguage].localeCompare(b.name[$currentLanguage]);
			}
		});

	function handleAddToCart(product: any) {
		addToCart({
			id: product.id,
			name: $currentLanguage === 'en' ? product.name.en : product.name.ku,
			price: product.price,
			image: product.image
		});
	}

	function renderStars(rating: number) {
		const stars = [];
		const fullStars = Math.floor(rating);
		const hasHalfStar = rating % 1 !== 0;

		for (let i = 0; i < fullStars; i++) {
			stars.push('★');
		}
		if (hasHalfStar) {
			stars.push('☆');
		}
		return stars.join('');
	}

	function getCategoryIcon(category: string) {
		switch (category.toLowerCase()) {
			case 'necklaces':
			case 'ملوانکە':
				return Heart;
			case 'bracelets':
			case 'بازن':
				return Sparkles;
			case 'earrings':
			case 'گوارە':
				return Star;
			case 'rings':
			case 'ئەنگوستیلە':
				return Crown;
			case 'jewelry sets':
			case 'کۆمەڵەی خشڵ':
				return Gift;
			case 'precious stones':
			case 'بەردی گرانبەها':
				return Gem;
			default:
				return Sparkles;
		}
	}
</script>

<div class="bg-cream min-h-screen">
	<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
		<!-- Page Header -->
		<div class="text-center mb-16">
			<div class="flex justify-center mb-6">
				<Gem size={60} class="text-rose-gold-400 animate-bounce-gentle" />
			</div>
			<h1 class="text-5xl font-serif font-bold text-charcoal mb-6">
				{#if $currentLanguage === 'en'}
					Jewelry Collection
				{:else}
					کۆمەڵەی خشڵ
				{/if}
			</h1>
			<p class="text-xl text-soft-gray max-w-3xl mx-auto leading-relaxed">
				{#if $currentLanguage === 'en'}
					Discover our exquisite collection of handcrafted jewelry pieces, each designed to celebrate your unique style and elegance
				{:else}
					کۆمەڵەیەکی ئەنیقی پارچە خشڵی دەستکرد بدۆزەرەوە کە هەریەکەیان بۆ ناساندنی ستایل و ئەنیقی تایبەتی تۆ دروستکراون
				{/if}
			</p>
		</div>

		<!-- Search and Filters -->
		<div class="jewelry-card p-8 mb-12">
			<div class="grid grid-cols-1 md:grid-cols-4 gap-6">
				<!-- Search -->
				<div class="relative">
					<div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
						<Search class="h-5 w-5 text-rose-gold-400" />
					</div>
					<input
						type="text"
						bind:value={searchTerm}
						placeholder={$currentLanguage === 'en' ? 'Search jewelry...' : 'گەڕان لە خشڵ...'}
						class="elegant-input w-full pl-12"
					/>
				</div>

				<!-- Category Filter -->
				<div>
					<select
						bind:value={selectedCategory}
						class="elegant-input w-full"
					>
						<option value="">
							{#if $currentLanguage === 'en'}
								All Categories
							{:else}
								هەموو جۆرەکان
							{/if}
						</option>
						{#each categories as category}
							<option value={category}>{category}</option>
						{/each}
					</select>
				</div>

				<!-- Sort -->
				<div>
					<select
						bind:value={sortBy}
						class="elegant-input w-full"
					>
						<option value="name">
							{#if $currentLanguage === 'en'}
								Sort by Name
							{:else}
								ڕیزکردن بە ناو
							{/if}
						</option>
						<option value="price">
							{#if $currentLanguage === 'en'}
								Sort by Price
							{:else}
								ڕیزکردن بە نرخ
							{/if}
						</option>
						<option value="rating">
							{#if $currentLanguage === 'en'}
								Sort by Rating
							{:else}
								ڕیزکردن بە هەڵسەنگاندن
							{/if}
						</option>
					</select>
				</div>

				<!-- View Mode -->
				<div class="flex space-x-3">
					<button
						on:click={() => viewMode = 'grid'}
						class="flex-1 flex items-center justify-center px-4 py-3 border-2 rounded-xl transition-all duration-200"
						class:bg-rose-gold-400={viewMode === 'grid'}
						class:text-white={viewMode === 'grid'}
						class:border-rose-gold-400={viewMode === 'grid'}
						class:bg-white={viewMode !== 'grid'}
						class:text-soft-gray={viewMode !== 'grid'}
						class:border-gray-300={viewMode !== 'grid'}
						class:hover:border-rose-gold-300={viewMode !== 'grid'}
					>
						<Grid size={20} />
					</button>
					<button
						on:click={() => viewMode = 'list'}
						class="flex-1 flex items-center justify-center px-4 py-3 border-2 rounded-xl transition-all duration-200"
						class:bg-rose-gold-400={viewMode === 'list'}
						class:text-white={viewMode === 'list'}
						class:border-rose-gold-400={viewMode === 'list'}
						class:bg-white={viewMode !== 'list'}
						class:text-soft-gray={viewMode !== 'list'}
						class:border-gray-300={viewMode !== 'list'}
						class:hover:border-rose-gold-300={viewMode !== 'list'}
					>
						<List size={20} />
					</button>
				</div>
			</div>
		</div>

		<!-- Products Count -->
		<div class="mb-8 text-center">
			<p class="text-soft-gray font-medium">
				{#if $currentLanguage === 'en'}
					Showing {filteredProducts.length} exquisite pieces
				{:else}
					پیشاندانی {filteredProducts.length} پارچەی ئەنیق
				{/if}
			</p>
		</div>

		{#if viewMode === 'grid'}
			<div class="jewelry-grid mb-16">
				{#each filteredProducts as product}
					<div class="jewelry-card overflow-hidden group cursor-pointer">
						<div class="relative bg-gradient-to-br from-accent-pink to-warm-beige h-64 flex items-center justify-center">
							<div class="text-rose-gold-400 opacity-20 group-hover:opacity-40 transition-opacity duration-300">
								<svelte:component this={getCategoryIcon($currentLanguage === 'en' ? product.category.en : product.category.ku)} size={80} />
							</div>
							<div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-300 flex items-center justify-center">
								<span class="text-rose-gold-400 font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-300">
									{#if $currentLanguage === 'en'}
										View Details
									{:else}
										بینینی وردەکارییەکان
									{/if}
								</span>
							</div>
						</div>
						<div class="p-6">
							<div class="flex items-center justify-between mb-2">
								<span class="text-xs font-medium text-rose-gold-400 uppercase tracking-wide">
									{#if $currentLanguage === 'en'}
										{product.category.en}
									{:else}
										{product.category.ku}
									{/if}
								</span>
								<span class="text-xs text-soft-gray">
									{#if $currentLanguage === 'en'}
										{product.collection.en}
									{:else}
										{product.collection.ku}
									{/if}
								</span>
							</div>
							<h3 class="font-serif font-semibold text-xl mb-3 text-charcoal">
								{#if $currentLanguage === 'en'}
									{product.name.en}
								{:else}
									{product.name.ku}
								{/if}
							</h3>
							<p class="text-soft-gray text-sm mb-3 leading-relaxed line-clamp-2">
								{#if $currentLanguage === 'en'}
									{product.description.en}
								{:else}
									{product.description.ku}
								{/if}
							</p>
							<p class="text-xs text-soft-gray mb-4">
								{#if $currentLanguage === 'en'}
									Material: {product.material.en}
								{:else}
									مادە: {product.material.ku}
								{/if}
							</p>

							<!-- Rating -->
							<div class="flex items-center mb-4">
								<span class="text-rose-gold-400 mr-2">{renderStars(product.rating)}</span>
								<span class="text-sm text-soft-gray">({product.rating})</span>
							</div>

							<div class="flex items-center justify-between">
								<span class="text-2xl font-serif font-bold text-rose-gold-400">£{product.price.toFixed(2)}</span>
								{#if product.inStock}
									<button
										on:click={() => handleAddToCart(product)}
										class="luxury-button text-sm px-6 py-2 group-hover:scale-105 transition-transform duration-200"
									>
										{#if $currentLanguage === 'en'}
											Add to Cart
										{:else}
											زیادکردن بۆ سەبەتە
										{/if}
									</button>
								{:else}
									<span class="text-red-500 text-sm font-medium bg-red-50 px-3 py-1 rounded-full">
										{#if $currentLanguage === 'en'}
											Sold Out
										{:else}
											فرۆشراوە
										{/if}
									</span>
								{/if}
							</div>
						</div>
					</div>
				{/each}
			</div>
		{:else}
			<div class="space-y-6">
				{#each filteredProducts as product}
					<div class="jewelry-card overflow-hidden group">
						<div class="flex">
							<div class="bg-gradient-to-br from-accent-pink to-warm-beige w-40 h-40 flex items-center justify-center flex-shrink-0">
								<div class="text-rose-gold-400 opacity-30 group-hover:opacity-50 transition-opacity duration-300">
									<svelte:component this={getCategoryIcon($currentLanguage === 'en' ? product.category.en : product.category.ku)} size={60} />
								</div>
							</div>
							<div class="flex-1 p-6">
								<div class="flex justify-between items-start">
									<div class="flex-1">
										<div class="flex items-center gap-3 mb-2">
											<span class="text-xs font-medium text-rose-gold-400 uppercase tracking-wide">
												{#if $currentLanguage === 'en'}
													{product.category.en}
												{:else}
													{product.category.ku}
												{/if}
											</span>
											<span class="text-xs text-soft-gray">
												{#if $currentLanguage === 'en'}
													{product.collection.en}
												{:else}
													{product.collection.ku}
												{/if}
											</span>
										</div>
										<h3 class="font-serif font-semibold text-xl mb-2 text-charcoal">
											{#if $currentLanguage === 'en'}
												{product.name.en}
											{:else}
												{product.name.ku}
											{/if}
										</h3>
										<p class="text-soft-gray text-sm mb-3 leading-relaxed">
											{#if $currentLanguage === 'en'}
												{product.description.en}
											{:else}
												{product.description.ku}
											{/if}
										</p>
										<p class="text-xs text-soft-gray mb-3">
											{#if $currentLanguage === 'en'}
												Material: {product.material.en}
											{:else}
												مادە: {product.material.ku}
											{/if}
										</p>

										<!-- Rating -->
										<div class="flex items-center">
											<span class="text-rose-gold-400 mr-2">{renderStars(product.rating)}</span>
											<span class="text-sm text-soft-gray">({product.rating})</span>
										</div>
									</div>
									<div class="text-right ml-6">
										<div class="text-2xl font-serif font-bold text-rose-gold-400 mb-4">£{product.price.toFixed(2)}</div>
										{#if product.inStock}
											<button
												on:click={() => handleAddToCart(product)}
												class="luxury-button px-6 py-3"
											>
												{#if $currentLanguage === 'en'}
													Add to Cart
												{:else}
													زیادکردن بۆ سەبەتە
												{/if}
											</button>
										{:else}
											<span class="text-red-500 text-sm font-medium bg-red-50 px-4 py-2 rounded-full">
												{#if $currentLanguage === 'en'}
													Sold Out
												{:else}
													فرۆشراوە
												{/if}
											</span>
										{/if}
									</div>
								</div>
							</div>
						</div>
					</div>
				{/each}
			</div>
		{/if}

		{#if filteredProducts.length === 0}
			<div class="text-center py-20">
				<Gem size={80} class="mx-auto mb-6 text-rose-gold-300" />
				<h3 class="text-2xl font-serif font-bold text-charcoal mb-4">
					{#if $currentLanguage === 'en'}
						No Jewelry Found
					{:else}
						هیچ خشڵێک نەدۆزرایەوە
					{/if}
				</h3>
				<p class="text-soft-gray text-lg max-w-md mx-auto">
					{#if $currentLanguage === 'en'}
						We couldn't find any jewelry pieces matching your search criteria. Try adjusting your filters or browse our full collection.
					{:else}
						نەمانتوانی هیچ پارچە خشڵێک بدۆزینەوە کە لەگەڵ مەرجەکانی گەڕانت بگونجێت. هەوڵ بدە فلتەرەکانت بگۆڕیت یان تەواوی کۆمەڵەکەمان بگەڕێیت.
					{/if}
				</p>
			</div>
		{/if}
	</div>
</div>
