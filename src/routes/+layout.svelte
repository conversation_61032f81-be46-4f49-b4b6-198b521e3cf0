<script lang="ts">
	import '../app.css';
	import { onMount } from 'svelte';
	import { user, loading, signOut } from '$lib/stores/auth.js';
	import { currentLanguage, setLanguage, t } from '$lib/stores/language.js';
	import { cartCount } from '$lib/stores/cart.js';
	import { Menu, X, ShoppingCart, User, Globe, LogOut, Crown, Heart } from 'lucide-svelte';

	let { children } = $props();

	let mobileMenuOpen = $state(false);
	let userMenuOpen = $state(false);
	let languageMenuOpen = $state(false);

	// Close menus when clicking outside
	function closeMenus() {
		mobileMenuOpen = false;
		userMenuOpen = false;
		languageMenuOpen = false;
	}

	// Handle language change
	function handleLanguageChange(lang: string) {
		setLanguage(lang);
		closeMenus();
	}

	// Handle logout
	async function handleLogout() {
		await signOut();
		closeMenus();
	}

	onMount(() => {
		// Close menus when clicking outside
		document.addEventListener('click', closeMenus);
		return () => {
			document.removeEventListener('click', closeMenus);
		};
	});
</script>

<div class="min-h-screen bg-cream">
	<!-- Navigation -->
	<nav class="bg-white shadow-jewelry sticky top-0 z-50 border-b border-rose-gold-200">
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
			<div class="flex justify-between h-18">
				<!-- Logo and main nav -->
				<div class="flex items-center">
					<!-- Logo -->
					<a href="/" class="flex items-center space-x-3">
						<img src="/logo/logo.svg" alt="Coco UK" class="h-12 w-12" />
						<div class="flex flex-col">
							<span class="text-2xl font-serif font-bold text-charcoal">Coco UK</span>
							<span class="text-xs text-rose-gold-400 font-medium tracking-wide">WOMEN'S JEWELRY</span>
						</div>
					</a>

					<!-- Desktop Navigation -->
					<div class="hidden md:ml-12 md:flex md:space-x-8">
						<a href="/" class="text-charcoal hover:text-rose-gold-400 px-3 py-2 text-sm font-medium transition-colors duration-200">
							{t('nav.home', $currentLanguage)}
						</a>
						<a href="/products" class="text-charcoal hover:text-rose-gold-400 px-3 py-2 text-sm font-medium transition-colors duration-200">
							{t('nav.jewelry', $currentLanguage)}
						</a>
						<a href="/categories" class="text-charcoal hover:text-rose-gold-400 px-3 py-2 text-sm font-medium transition-colors duration-200">
							{t('nav.categories', $currentLanguage)}
						</a>
					</div>
				</div>

				<!-- Right side navigation -->
				<div class="flex items-center space-x-6">
					<!-- Language Switcher -->
					<div class="relative">
						<button
							onclick={(e) => { e.stopPropagation(); languageMenuOpen = !languageMenuOpen; }}
							class="flex items-center space-x-2 text-charcoal hover:text-rose-gold-400 transition-colors duration-200"
						>
							<Globe size={20} />
							<span class="hidden sm:block text-sm font-medium">
								{$currentLanguage === 'en' ? 'EN' : 'کو'}
							</span>
						</button>
						{#if languageMenuOpen}
							<div class="absolute right-0 mt-2 w-36 bg-white rounded-xl shadow-jewelry py-2 z-50 border border-rose-gold-200">
								<button
									onclick={() => handleLanguageChange('en')}
									class="block w-full text-left px-4 py-2 text-sm text-charcoal hover:bg-accent-pink transition-colors duration-200"
									class:bg-accent-pink={$currentLanguage === 'en'}
									class:text-rose-gold-400={$currentLanguage === 'en'}
								>
									English
								</button>
								<button
									onclick={() => handleLanguageChange('ku')}
									class="block w-full text-left px-4 py-2 text-sm text-charcoal hover:bg-accent-pink transition-colors duration-200"
									class:bg-accent-pink={$currentLanguage === 'ku'}
									class:text-rose-gold-400={$currentLanguage === 'ku'}
								>
									کوردی سۆرانی
								</button>
							</div>
						{/if}
					</div>

					<!-- Cart -->
					<a href="/cart" class="relative text-charcoal hover:text-rose-gold-400 transition-colors duration-200 group">
						<ShoppingCart size={24} class="group-hover:scale-110 transition-transform duration-200" />
						{#if $cartCount > 0}
							<span class="absolute -top-2 -right-2 bg-rose-gold-400 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium animate-bounce-gentle">
								{$cartCount}
							</span>
						{/if}
					</a>

					<!-- User Menu -->
					{#if !$loading}
						{#if $user}
							<div class="relative">
								<button
									onclick={(e) => { e.stopPropagation(); userMenuOpen = !userMenuOpen; }}
									class="flex items-center space-x-1 text-charcoal hover:text-rose-gold-400 transition-colors duration-200 group"
								>
									<User size={24} class="group-hover:scale-110 transition-transform duration-200" />
								</button>
								{#if userMenuOpen}
									<div class="absolute right-0 mt-2 w-48 bg-white rounded-xl shadow-jewelry py-2 z-50 border border-rose-gold-200">
										<a href="/account" class="block px-4 py-2 text-sm text-charcoal hover:bg-accent-pink transition-colors duration-200">
											{t('nav.account', $currentLanguage)}
										</a>
										{#if $user?.email === '<EMAIL>'}
											<a href="/admin" class="block px-4 py-2 text-sm text-charcoal hover:bg-accent-pink transition-colors duration-200">
												{t('nav.admin', $currentLanguage)}
											</a>
										{/if}
										<button
											onclick={handleLogout}
											class="block w-full text-left px-4 py-2 text-sm text-charcoal hover:bg-accent-pink transition-colors duration-200"
										>
											<div class="flex items-center space-x-2">
												<LogOut size={16} />
												<span>{t('nav.logout', $currentLanguage)}</span>
											</div>
										</button>
									</div>
								{/if}
							</div>
						{:else}
							<div class="flex items-center space-x-3">
								<a href="/login" class="text-charcoal hover:text-rose-gold-400 text-sm font-medium transition-colors duration-200">
									{t('nav.login', $currentLanguage)}
								</a>
								<a href="/register" class="luxury-button text-sm">
									{t('nav.register', $currentLanguage)}
								</a>
							</div>
						{/if}
					{/if}

					<!-- Mobile menu button -->
					<button
						onclick={(e) => { e.stopPropagation(); mobileMenuOpen = !mobileMenuOpen; }}
						class="md:hidden text-charcoal hover:text-rose-gold-400 transition-colors duration-200"
					>
						{#if mobileMenuOpen}
							<X size={24} />
						{:else}
							<Menu size={24} />
						{/if}
					</button>
				</div>
			</div>
		</div>

		<!-- Mobile Navigation -->
		{#if mobileMenuOpen}
			<div class="md:hidden bg-white border-t border-rose-gold-200">
				<div class="px-4 pt-4 pb-6 space-y-2">
					<a href="/" class="block px-3 py-3 text-base font-medium text-charcoal hover:text-rose-gold-400 hover:bg-accent-pink rounded-lg transition-all duration-200">
						{t('nav.home', $currentLanguage)}
					</a>
					<a href="/products" class="block px-3 py-3 text-base font-medium text-charcoal hover:text-rose-gold-400 hover:bg-accent-pink rounded-lg transition-all duration-200">
						{t('nav.jewelry', $currentLanguage)}
					</a>
					<a href="/categories" class="block px-3 py-3 text-base font-medium text-charcoal hover:text-rose-gold-400 hover:bg-accent-pink rounded-lg transition-all duration-200">
						{t('nav.categories', $currentLanguage)}
					</a>
				</div>
			</div>
		{/if}
	</nav>

	<!-- Main Content -->
	<main>
		{@render children()}
	</main>

	<!-- Footer -->
	<footer class="bg-charcoal text-white">
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
			<div class="grid grid-cols-1 md:grid-cols-4 gap-8">
				<!-- Company Info -->
				<div class="col-span-1 md:col-span-2">
					<div class="flex items-center space-x-3 mb-6">
						<img src="/logo/logo.svg" alt="Coco UK" class="h-12 w-12" />
						<div class="flex flex-col">
							<span class="text-2xl font-serif font-bold text-white">Coco UK</span>
							<span class="text-sm text-rose-gold-300 font-medium tracking-wide">WOMEN'S JEWELRY</span>
						</div>
					</div>
					<p class="text-gray-300 mb-6 leading-relaxed">
						{t('footer.description', $currentLanguage)}
					</p>
					<div class="flex space-x-4">
						<Heart size={20} class="text-rose-gold-300" />
						<Crown size={20} class="text-rose-gold-300" />
					</div>
				</div>

				<!-- Quick Links -->
				<div>
					<h3 class="text-lg font-serif font-semibold mb-6 text-rose-gold-300">Quick Links</h3>
					<ul class="space-y-3">
						<li><a href="/about" class="text-gray-300 hover:text-rose-gold-300 transition-colors duration-200">{t('footer.about', $currentLanguage)}</a></li>
						<li><a href="/contact" class="text-gray-300 hover:text-rose-gold-300 transition-colors duration-200">{t('footer.contact', $currentLanguage)}</a></li>
						<li><a href="/privacy" class="text-gray-300 hover:text-rose-gold-300 transition-colors duration-200">{t('footer.privacy', $currentLanguage)}</a></li>
						<li><a href="/terms" class="text-gray-300 hover:text-rose-gold-300 transition-colors duration-200">{t('footer.terms', $currentLanguage)}</a></li>
					</ul>
				</div>

				<!-- Contact Info -->
				<div>
					<h3 class="text-lg font-serif font-semibold mb-6 text-rose-gold-300">{t('footer.contact', $currentLanguage)}</h3>
					<div class="text-gray-300 space-y-3">
						<p class="hover:text-rose-gold-300 transition-colors duration-200">Email: <EMAIL></p>
						<p class="hover:text-rose-gold-300 transition-colors duration-200">Phone: +44 ************</p>
					</div>
				</div>
			</div>

			<div class="border-t border-gray-700 mt-12 pt-8 text-center">
				<p class="text-gray-400">&copy; 2024 Coco UK. All rights reserved. | Luxury Women's Jewelry</p>
			</div>
		</div>
	</footer>
</div>
